export type TMessageCode = keyof typeof MAP_MESSAGE;

export const MAP_MESSAGE: Record<string, string> = {
    SUCCESS_SIGN_IN: "Successfully signed in",
    SUCCESS_SIGN_UP: "Account created successfully",
    SUCCESS_GET_NEW_ACCESS_TOKEN: "Successfully generated new access token",
    SUCCESS_BLOCK_ACCOUNT: "Successfully blocked account",
    SUCCESS_UNBLOCK_ACCOUNT: "Successfully unblocked account",
    PASSWORD_NOT_MATCH: "Wrong password",
    SUCCESS_LOGOUT: "Successfully logged out",
    EMAIL_VALID: "Email is valid",
    ALREADY_IN_SOFT_DELETE: "Item is already in soft delete status",
    NOT_IN_SOFT_DELETE: "Item is not in soft delete status",
    PASSWORD_NOT_STRONG_ENOUGH: "Password is not strong enough",
    WRONG_PASSWORD: "Wrong password",
    EMAIL_ALREADY_EXIST: "Email already exists",
    USERNAME_ALREADY_EXIST: "Username already exists",
    EMAIL_NOT_FOUND: "No account found with the email provided",
    USER_ID_NOT_FOUND: "No account found with the user ID provided",
    USERNAME_NOT_FOUND: "No account found with the username provided",
    WRONG_REFRESH_TOKEN: "The refresh token provided doesn't match the server",
    ACCOUNT_ALREADY_BLOCKED: "Account is already blocked",
    ACCOUNT_NOT_BLOCKED: "Account is not blocked",
    INVALID_SEARCH_TYPE_VALUE: "Invalid search type value",
    SUCCESS_GET_ALL_ACCOUNTS: "Successfully fetched all accounts",
    SUCCESS_GET_ALL_APPS: "Successfully fetched all apps",
    APP_NOT_FOUND: "App not found",
    SUCCESS_GET_APP_INFO: "Successfully fetched app information",
    SUCCESS_ADD_APP: "Successfully added new app",
    SUCCESS_UPDATE_APP_INFO: "Successfully updated app information",
    SUCCESS_UPDATE_APP_DISPLAY_STATUS: "Successfully updated app display status",
    SUCCESS_DELETE_APP: "Successfully deleted app",
    INVALID_FILTER_APP: "Invalid app filter",
    INVALID_UPDATE_ACTIVE_STATUS_ACTION: "Invalid update active status action",
    SUCCESS_GET_ALL_CERTS: "Successfully fetched all certificates",
    MISS_CERT_IMAGE: "Certificate image is missing",
    SUCCESS_ADD_CERT: "Successfully added new certificate",
    SUCCESS_GET_CERT_INFO: "Successfully fetched certificate information",
    CERT_NOT_FOUND: "Certificate not found",
    SUCCESS_UPDATE_CERT_INFO: "Successfully updated certificate information",
    SUCCESS_DELETE_CERT: "Successfully deleted certificate",
    SUCCESS_RECOVER_CERT: "Successfully recovered certificate",
    SUCCESS_GET_ALL_EDU: "Successfully fetched all education records",
    EDU_NOT_FOUND: "Education record not found",
    SUCCESS_GET_EDU_INFO: "Successfully fetched education information",
    SUCCESS_ADD_EDU: "Successfully added new education record",
    SUCCESS_UPDATE_EDU_INFO: "Successfully updated education information",
    SUCCESS_DELETE_EDU: "Successfully deleted education record",
    SUCCESS_RECOVER_EDU: "Successfully recovered education record",
    EMPLOYMENT_NOT_FOUND: "Employment record not found",
    SUCCESS_GET_EMPLOYMENT_INFO: "Successfully fetched employment information",
    SUCCESS_GET_ALL_EMPLOYMENTS: "Successfully fetched all employment records",
    SUCCESS_ADD_EMPLOYMENT: "Successfully added new employment record",
    SUCCESS_UPDATE_EMPLOYMENT_INFO: "Successfully updated employment information",
    SUCCESS_DELETE_EMPLOYMENT: "Successfully deleted employment record",
    SUCCESS_RECOVER_EMPLOYMENT: "Successfully recovered employment record",
    SUCCESS_GET_ALL_GC_ROOMS: "Successfully fetched all game card rooms",
    GC_ROOM_NOT_FOUND: "Game card room not found",
    SUCCESS_GET_ROOM_INFO: "Successfully fetched room information",
    SUCCESS_CREATE_GC_ROOM: "Successfully created game card room",
    SUCCESS_CREATE_GC_MATCH_RESULT: "Successfully created match result",
    SUCCESS_UPDATE_GC_ROOM_CONFIG: "Successfully updated room configuration",
    SUCCESS_GET_GC_ROOM_RESULTS: "Successfully fetched room results",
    SUCCESS_CLOSE_GC_ROOM: "Successfully closed game card room",
    SUCCESS_REOPEN_GC_ROOM: "Successfully reopened game card room",
    SUCCESS_DELETE_MATCH_RESULT: "Successfully deleted match result",
    SUCCESS_ADD_PROJECT: "Successfully added new project",
    SUCCESS_GET_ALL_PROJECTS: "Successfully fetched all projects",
    PROJECT_NOT_FOUND: "Project not found",
    SUCCESS_GET_PROJECT_INFO: "Successfully fetched project information",
    SUCCESS_UPDATE_PROJECT_INFO: "Successfully updated project information",
    SUCCESS_DELETE_PROJECT: "Successfully deleted project",
    SUCCESS_GET_ALL_PROJECT_GROUPS: "Successfully fetched all project groups",
    SUCCESS_CREATE_PROJECT_GROUP: "Successfully created project group",
    PROJECT_GROUP_NOT_FOUND: "Project group not found",
    SUCCESS_UPDATE_PROJECT_GROUP_INFO: "Successfully updated project group information",
    SUCCESS_DELETE_PROJECT_GROUP: "Successfully deleted project group",
    SUCCESS_RECOVER_PROJECT_GROUP: "Successfully recovered project group",
    EXPIRED_REFRESH_TOKEN: "Your sessions has expired. Please sign in again.",
    NO_PERMISSION: "You don't have permission to perform this."
}

