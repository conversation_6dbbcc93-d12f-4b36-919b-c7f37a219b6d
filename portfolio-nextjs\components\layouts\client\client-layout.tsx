import Container from "@/components/shared/container/container";
import Client<PERSON>eader from "@/components/shared/partials/client-header";
import Sidebar from "@/components/shared/partials/sidebar";

export default function ClientLayout({ children }: { children: React.ReactNode }) {
	return (
		<div className={"relative"}>
			<ClientHeader />
			<div className={"relative lg:mt-44 mt-32 min-h-[150vh] h-max bg-white flex justify-center xl:px-0 lg:px-8"}>
				<Container className={"2xl:max-w-7xl xl:max-w-6xl lg:max-w-5xl pt-0"}>
					<Sidebar />
					{children}
				</Container>
			</div>
		</div>
	);
}
